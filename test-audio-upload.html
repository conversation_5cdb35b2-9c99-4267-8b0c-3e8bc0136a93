<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест загрузки аудио для слов и предложений</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Тест загрузки аудио для слов и предложений</h1>
    
    <div class="form-section">
        <h2>Создание слова с аудио файлом</h2>
        <form id="wordForm">
            <div class="form-group">
                <label for="wordKaz">Текст на казахском:</label>
                <input type="text" id="wordKaz" name="kaz_plaintext" required placeholder="сәлем">
            </div>
            <div class="form-group">
                <label for="wordRus">Текст на русском:</label>
                <input type="text" id="wordRus" name="rus_plaintext" required placeholder="привет">
            </div>
            <div class="form-group">
                <label for="wordAudio">Аудио файл (необязательно):</label>
                <input type="file" id="wordAudio" name="audio" accept=".mp3,.wav,.ogg,.m4a">
            </div>
            <button type="submit">Создать слово</button>
        </form>
        <div id="wordResult" class="result" style="display: none;"></div>
    </div>

    <div class="form-section">
        <h2>Создание предложения с аудио файлом</h2>
        <form id="sentenceForm">
            <div class="form-group">
                <label for="sentenceKaz">Текст на казахском:</label>
                <input type="text" id="sentenceKaz" name="kaz_plaintext" required placeholder="Сәлем, қалың қалай?">
            </div>
            <div class="form-group">
                <label for="sentenceRus">Текст на русском:</label>
                <input type="text" id="sentenceRus" name="rus_plaintext" required placeholder="Привет, как дела?">
            </div>
            <div class="form-group">
                <label for="sentenceAudio">Аудио файл (необязательно):</label>
                <input type="file" id="sentenceAudio" name="audio" accept=".mp3,.wav,.ogg,.m4a">
            </div>
            <button type="submit">Создать предложение</button>
        </form>
        <div id="sentenceResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/v1';
        
        // Здесь нужно будет добавить токен аутентификации
        const AUTH_TOKEN = 'your_auth_token_here';

        async function createWordWithAudio(formData) {
            try {
                const response = await fetch(`${API_BASE}/word/with-audio`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        async function createSentenceWithAudio(formData) {
            try {
                const response = await fetch(`${API_BASE}/sentence/with-audio`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`
                    },
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                throw error;
            }
        }

        function showResult(elementId, message, isError = false) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.style.display = 'block';
        }

        document.getElementById('wordForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            
            try {
                const result = await createWordWithAudio(formData);
                showResult('wordResult', `Слово создано успешно! ID: ${result.word.id}, Audio URL: ${result.word.audio_url || 'не загружено'}`);
            } catch (error) {
                showResult('wordResult', `Ошибка: ${error.message}`, true);
            }
        });

        document.getElementById('sentenceForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            
            try {
                const result = await createSentenceWithAudio(formData);
                showResult('sentenceResult', `Предложение создано успешно! ID: ${result.sentence.id}, Audio URL: ${result.sentence.audio_url || 'не загружено'}`);
            } catch (error) {
                showResult('sentenceResult', `Ошибка: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>
