# Kazakh-Lingo API Endpoints - Детальное описание

## Базовая информация

- **Base URL**: `http://localhost:8080`
- **Аутентификация**: <PERSON><PERSON> (JWT)
- **Content-Type**: `application/json`
- **Кодировка**: UTF-8

## Аутентификация

### POST /v1/auth/register
Регистрация нового пользователя

**Параметры запроса:**
```json
{
  "name": "string (required, max 500 chars)",
  "surname": "string (required, max 500 chars)", 
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars)",
  "imageUrl": "string (optional)"
}
```

**Ответ (201 Created):**
```json
{
  "tokens": {
    "access_token": "jwt_access_token",
    "refresh_token": "jwt_refresh_token"
  },
  "user": {
    "id": 1,
    "name": "Айдар",
    "surname": "Нурланов",
    "email": "<EMAIL>",
    "image_url": "",
    "activated": true,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

**Ошибки:**
- `400` - Неверные данные
- `422` - Ошибки валидации (email уже существует)

### POST /v1/auth/login
Вход в систему

**Параметры запроса:**
```json
{
  "email": "string (required)",
  "password": "string (required)"
}
```

**Ответ (200 OK):**
```json
{
  "tokens": {
    "access_token": "jwt_access_token",
    "refresh_token": "jwt_refresh_token"
  },
  "user": {
    "id": 1,
    "name": "Айдар",
    "surname": "Нурланов",
    "email": "<EMAIL>",
    "image_url": "",
    "activated": true,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

**Ошибки:**
- `400` - Неверные данные
- `401` - Неверные учетные данные

## Управление словами

### POST /v1/word
Создание нового слова

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "kaz_plaintext": "string (required)",
  "rus_plaintext": "string (required)",
  "audio_url": "string (optional)"
}
```

**Ответ (201 Created):**
```json
{
  "word": {
    "id": 1,
    "kaz_plaintext": "сәлем",
    "rus_plaintext": "привет",
    "audio_url": "http://minio:9000/klingo-audio/audio-file.mp3"
  }
}
```

### POST /v1/word/with-audio
Создание нового слова с загрузкой аудио файла

**Требует аутентификации**: Да

**Content-Type**: `multipart/form-data`

**Параметры запроса:**
- `kaz_plaintext` (string, required) - Текст на казахском языке
- `rus_plaintext` (string, required) - Текст на русском языке
- `audio` (file, optional) - Аудио файл (mp3, wav, ogg, m4a, до 10MB)

**Пример запроса:**
```bash
curl -X POST http://localhost:4000/v1/word/with-audio \
  -H "Authorization: Bearer your_token" \
  -F "kaz_plaintext=сәлем" \
  -F "rus_plaintext=привет" \
  -F "audio=@audio-file.mp3"
```

**Ответ (201 Created):**
```json
{
  "word": {
    "id": 1,
    "kaz_plaintext": "сәлем",
    "rus_plaintext": "привет",
    "audio_url": "http://minio:9000/klingo-audio/generated-filename.mp3"
  }
}
```

### GET /v1/word
Получение всех слов

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "words": [
    {
      "id": 1,
      "kaz_plaintext": "сәлем",
      "rus_plaintext": "привет",
      "audio_url": "http://minio:9000/klingo-audio/audio-file.mp3"
    }
  ]
}
```

## Управление предложениями

### POST /v1/sentence
Создание нового предложения

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "kaz_plaintext": "string (required)",
  "rus_plaintext": "string (required)",
  "audio_url": "string (optional)"
}
```

**Ответ (201 Created):**
```json
{
  "sentence": {
    "id": 1,
    "kaz_plaintext": "Сәлем, қалың қалай?",
    "rus_plaintext": "Привет, как дела?",
    "audio_url": "http://minio:9000/klingo-audio/sentence-audio.mp3"
  }
}
```

### POST /v1/sentence/with-audio
Создание нового предложения с загрузкой аудио файла

**Требует аутентификации**: Да

**Content-Type**: `multipart/form-data`

**Параметры запроса:**
- `kaz_plaintext` (string, required) - Текст на казахском языке
- `rus_plaintext` (string, required) - Текст на русском языке
- `audio` (file, optional) - Аудио файл (mp3, wav, ogg, m4a, до 10MB)

**Пример запроса:**
```bash
curl -X POST http://localhost:4000/v1/sentence/with-audio \
  -H "Authorization: Bearer your_token" \
  -F "kaz_plaintext=Сәлем, қалың қалай?" \
  -F "rus_plaintext=Привет, как дела?" \
  -F "audio=@sentence-audio.mp3"
```

**Ответ (201 Created):**
```json
{
  "sentence": {
    "id": 1,
    "kaz_plaintext": "Сәлем, қалың қалай?",
    "rus_plaintext": "Привет, как дела?",
    "audio_url": "http://minio:9000/klingo-audio/generated-filename.mp3"
  }
}
```

### GET /v1/sentence
Получение предложения по ID

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID предложения

**Ответ (200 OK):**
```json
{
  "sentence": {
    "id": 1,
    "kaz_plaintext": "Сәлем, қалың қалай?",
    "rus_plaintext": "Привет, как дела?",
    "audio_url": "http://minio:9000/klingo-audio/sentence-audio.mp3"
  }
}
```

### GET /v1/sentence/all
Получение всех предложений

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "sentences": [
    {
      "id": 1,
      "kaz_plaintext": "Сәлем, қалың қалай?",
      "rus_plaintext": "Привет, как дела?",
      "audio_url": "http://minio:9000/klingo-audio/sentence-audio.mp3"
    }
  ]
}
```

## Управление вопросами

### POST /v1/questions
Создание нового вопроса

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "type": "string (required)",
  "words": [
    {
      "id": 1,
      "kaz_plaintext": "сәлем",
      "rus_plaintext": "привет",
      "audio_url": "http://minio:9000/klingo-audio/word.mp3",
      "sequence_order": 1
    }
  ],
  "correct_answer": "string (required)",
  "image_url": "string (optional)"
}
```

**Ответ (201 Created):**
```json
{
  "question": {
    "id": 1,
    "type": "translation",
    "words": [...],
    "correct_answer": "привет",
    "image_url": ""
  }
}
```

### GET /v1/questions
Получение вопроса по ID

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID вопроса

**Ответ (200 OK):**
```json
{
  "question": {
    "id": 1,
    "type": "translation",
    "words": [
      {
        "id": 1,
        "kaz_plaintext": "сәлем",
        "rus_plaintext": "привет",
        "audio_url": "http://minio:9000/klingo-audio/word.mp3"
      }
    ],
    "correct_answer": "привет",
    "image_url": ""
  }
}
```

### GET /v1/questions/all
Получение всех вопросов

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "questions": [
    {
      "id": 1,
      "type": "translation",
      "words": [...],
      "correct_answer": "привет",
      "image_url": ""
    }
  ]
}
```

## Управление теориями

### POST /v1/theory
Создание новой теории

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "title": "string (required)",
  "description": "string (required)",
  "module_id": "integer (required)",
  "tags": ["string"],
  "example_ids": [1, 2, 3]
}
```

**Ответ (201 Created):**
```json
{
  "theory": {
    "id": 1,
    "title": "Приветствие",
    "description": "Основные фразы приветствия на казахском языке",
    "module_id": 1,
    "tags": ["приветствие", "базовый"],
    "example_ids": [1, 2],
    "examples": [
      {
        "id": 1,
        "kaz_plaintext": "Сәлем",
        "rus_plaintext": "Привет",
        "audio_url": "..."
      }
    ],
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### GET /v1/theory
Получение теории по ID

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID теории

**Ответ (200 OK):**
```json
{
  "theory": {
    "id": 1,
    "title": "Приветствие",
    "description": "Основные фразы приветствия на казахском языке",
    "module_id": 1,
    "tags": ["приветствие", "базовый"],
    "example_ids": [1, 2],
    "examples": [...],
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### GET /v1/theory/all
Получение всех теорий

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "theories": [
    {
      "id": 1,
      "title": "Приветствие",
      "description": "Основные фразы приветствия на казахском языке",
      "module_id": 1,
      "tags": ["приветствие", "базовый"],
      "example_ids": [1, 2],
      "examples": [...],
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### PUT /v1/theory
Обновление теории

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "id": "integer (required)",
  "title": "string (optional)",
  "description": "string (optional)",
  "module_id": "integer (optional)",
  "tags": ["string"] (optional),
  "example_ids": [1, 2, 3] (optional)
}
```

**Ответ (200 OK):**
```json
{
  "theory": {
    "id": 1,
    "title": "Обновленное название",
    "description": "Обновленное описание",
    "module_id": 1,
    "tags": ["обновленный", "тег"],
    "example_ids": [1, 2, 3],
    "examples": [...],
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### DELETE /v1/theory
Удаление теории

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID теории

**Ответ (200 OK):**
```json
{
  "message": "theory deleted successfully"
}
```

## Управление модулями

### POST /v1/module
Создание нового модуля

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "name": "string (required)",
  "theory_ids": [1, 2, 3],
  "question_ids": [1, 2, 3],
  "pre_requisite_ids": [1],
  "level": "integer (required)"
}
```

**Ответ (201 Created):**
```json
{
  "module": {
    "id": 1,
    "name": "Базовые приветствия",
    "theory_ids": [1, 2],
    "question_ids": [1, 2, 3],
    "questions": [],
    "theories": [],
    "pre_requisite_ids": [],
    "level": 1,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### GET /v1/module/all
Получение всех модулей

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "modules": [
    {
      "id": 1,
      "name": "Базовые приветствия",
      "theory_ids": [1, 2],
      "question_ids": [1, 2, 3],
      "questions": [],
      "theories": [],
      "pre_requisite_ids": [],
      "level": 1,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### GET /v1/module
Получение полного модуля с контентом

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID модуля

**Ответ (200 OK):**
```json
{
  "module": {
    "id": 1,
    "name": "Базовые приветствия",
    "theory_ids": [1, 2],
    "question_ids": [1, 2, 3],
    "questions": [
      {
        "id": 1,
        "type": "translation",
        "words": [...],
        "correct_answer": "привет",
        "image_url": ""
      }
    ],
    "theories": [
      {
        "id": 1,
        "title": "Приветствие",
        "description": "Основные фразы приветствия",
        "module_id": 1,
        "tags": ["приветствие"],
        "example_ids": [1],
        "examples": [...],
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pre_requisite_ids": [],
    "level": 1,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### PUT /v1/module
Обновление модуля

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "id": "integer (required)",
  "name": "string (optional)",
  "theory_ids": [1, 2, 3] (optional),
  "question_ids": [1, 2, 3] (optional),
  "pre_requisite_ids": [1] (optional),
  "level": "integer (optional)"
}
```

**Ответ (200 OK):**
```json
{
  "module": {
    "id": 1,
    "name": "Обновленное название модуля",
    "theory_ids": [1, 2, 3],
    "question_ids": [1, 2, 3, 4],
    "questions": [],
    "theories": [],
    "pre_requisite_ids": [1],
    "level": 2,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### DELETE /v1/module
Удаление модуля

**Требует аутентификации**: Да

**Query параметры:**
- `id` (required) - ID модуля

**Ответ (200 OK):**
```json
{
  "message": "module deleted successfully"
}
```

### GET /v1/module-user-progress/{id}
Получение прогресса пользователя по модулям

**Требует аутентификации**: Да

**Path параметры:**
- `id` (required) - ID пользователя

**Ответ (200 OK):**
```json
{
  "user_id": 1,
  "passed_modules": [
    {
      "module_id": 1,
      "module_name": "Базовые приветствия",
      "completed_at": "2024-01-01T15:30:00Z",
      "best_time": "00:03:45",
      "attempts": 2
    }
  ],
  "total_modules": 10,
  "completion_percentage": 10.0
}
```

## Управление прогрессом

### POST /v1/progress/save
Сохранение прогресса пользователя

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "user_id": "integer (required)",
  "module_id": "integer (required)",
  "mistaken_question_ids": [2, 5],
  "time": "string (required, format: HH:MM:SS)"
}
```

**Ответ (201 Created):**
```json
{
  "progress": {
    "id": 1,
    "user_id": 1,
    "module_id": 1,
    "mistaken_question_ids": [2, 5],
    "time": "00:05:30",
    "try_count": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### GET /v1/progress/streak/{id}
Получение серии дней обучения пользователя

**Требует аутентификации**: Да

**Path параметры:**
- `id` (required) - ID пользователя

**Ответ (200 OK):**
```json
{
  "user_id": 1,
  "current_streak": 7,
  "max_streak": 15,
  "last_activity": "2024-01-01T12:00:00Z"
}
```

## Управление достижениями

### GET /v1/achievements/{id}
Получение достижений пользователя

**Требует аутентификации**: Да

**Path параметры:**
- `id` (required) - ID пользователя

**Ответ (200 OK):**
```json
{
  "user_id": 1,
  "achievements": [
    {
      "achievement_id": 1,
      "user_id": 1,
      "progress": 100,
      "achieved": true,
      "achievement": {
        "id": 1,
        "name": "Первые шаги",
        "description": "Завершите первый модуль",
        "type": "module_completion",
        "target": 1,
        "created_at": "2024-01-01T12:00:00Z"
      }
    }
  ],
  "total_achievements": 15,
  "unlocked_count": 3
}
```

### POST /v1/achievements
Создание нового достижения

**Требует аутентификации**: Да (Admin)

**Параметры запроса:**
```json
{
  "name": "string (required)",
  "description": "string (required)",
  "type": "string (required)",
  "target": "integer (required)"
}
```

**Ответ (201 Created):**
```json
{
  "achievement": {
    "id": 1,
    "name": "Первые шаги",
    "description": "Завершите первый модуль",
    "type": "module_completion",
    "target": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## Управление файлами

### POST /v1/files/upload/audio
Загрузка аудио файла

**Требует аутентификации**: Да

**Content-Type**: `multipart/form-data`

**Параметры:**
- `audio` (file) - аудио файл (mp3, wav, ogg, m4a, max 10MB)

**Ответ (201 Created):**
```json
{
  "message": "Audio file uploaded successfully",
  "file_url": "http://minio:9000/klingo-audio/uuid-filename.mp3",
  "file_name": "uuid-filename.mp3",
  "file_size": 1024000,
  "content_type": "audio/mpeg"
}
```

**Ошибки:**
- `400` - Неверный тип файла или размер превышен
- `500` - Ошибка загрузки

### POST /v1/files/upload/image
Загрузка изображения

**Требует аутентификации**: Да

**Content-Type**: `multipart/form-data`

**Параметры:**
- `image` (file) - изображение (jpg, jpeg, png, gif, webp, max 5MB)

**Ответ (201 Created):**
```json
{
  "message": "Image file uploaded successfully",
  "file_url": "http://minio:9000/klingo-images/uuid-filename.jpg",
  "file_name": "uuid-filename.jpg",
  "file_size": 512000,
  "content_type": "image/jpeg"
}
```

### POST /v1/files/upload/multiple
Множественная загрузка файлов

**Требует аутентификации**: Да

**Content-Type**: `multipart/form-data`

**Параметры:**
- `files` (files) - массив файлов (аудио и изображения, max 50MB общий размер)

**Ответ (200 OK):**
```json
{
  "message": "Files processed",
  "uploaded_files": [
    {
      "file_name": "uuid-audio.mp3",
      "file_url": "http://minio:9000/klingo-audio/uuid-audio.mp3",
      "file_type": "audio"
    },
    {
      "file_name": "uuid-image.jpg",
      "file_url": "http://minio:9000/klingo-images/uuid-image.jpg",
      "file_type": "image"
    }
  ],
  "errors": [],
  "total_uploaded": 2,
  "total_errors": 0
}
```

### DELETE /v1/files/delete
Удаление файла

**Требует аутентификации**: Да

**Query параметры:**
- `url` (required) - URL файла для удаления

**Ответ (200 OK):**
```json
{
  "message": "File deleted successfully",
  "deleted_url": "http://minio:9000/klingo-audio/uuid-filename.mp3"
}
```

### GET /v1/files/list
Получение списка файлов

**Требует аутентификации**: Да

**Query параметры:**
- `bucket` (required) - имя bucket (klingo-audio или klingo-images)
- `prefix` (optional) - префикс для фильтрации файлов

**Ответ (200 OK):**
```json
{
  "bucket": "klingo-audio",
  "prefix": "",
  "files": [
    "uuid-file1.mp3",
    "uuid-file2.wav",
    "folder/uuid-file3.ogg"
  ],
  "count": 3
}
```

## Системные эндпоинты

### GET /v1/healthcheck
Проверка состояния системы

**Требует аутентификации**: Нет

**Ответ (200 OK):**
```json
{
  "status": "available",
  "system_info": {
    "environment": "production",
    "version": "1.0.0"
  }
}
```

### GET /debug/vars
Метрики приложения

**Требует аутентификации**: Нет

**Ответ (200 OK):**
```json
{
  "cmdline": ["./sport-hub"],
  "memstats": {
    "Alloc": 1234567,
    "TotalAlloc": 9876543,
    "Sys": 12345678,
    "NumGC": 42
  },
  "total_requests_received": 1000,
  "total_responses_sent": 1000,
  "total_processing_time_μs": 50000000,
  "total_responses_sent_by_status": {
    "200": 800,
    "400": 50,
    "401": 30,
    "404": 20,
    "500": 10
  }
}

## Коды состояния HTTP

### Успешные ответы (2xx)
- `200 OK` - Запрос выполнен успешно
- `201 Created` - Ресурс создан успешно

### Ошибки клиента (4xx)
- `400 Bad Request` - Неверные параметры запроса
- `401 Unauthorized` - Требуется аутентификация
- `403 Forbidden` - Недостаточно прав доступа
- `404 Not Found` - Ресурс не найден
- `409 Conflict` - Конфликт данных (например, дублирование email)
- `422 Unprocessable Entity` - Ошибки валидации данных
- `429 Too Many Requests` - Превышен лимит запросов

### Ошибки сервера (5xx)
- `500 Internal Server Error` - Внутренняя ошибка сервера

## Примеры ошибок

### Ошибка валидации (422)
```json
{
  "error": {
    "email": "must be a valid email address",
    "password": "must be at least 8 bytes long"
  }
}
```

### Ошибка аутентификации (401)
```json
{
  "error": "invalid authentication credentials"
}
```

### Ошибка не найден (404)
```json
{
  "error": "the requested resource could not be found"
}
```

### Превышен лимит запросов (429)
```json
{
  "error": "rate limit exceeded"
}
```

### Внутренняя ошибка сервера (500)
```json
{
  "error": "the server encountered a problem and could not process your request"
}
```

## Заголовки запросов

### Обязательные заголовки для аутентифицированных запросов:
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

### Для загрузки файлов:
```
Authorization: Bearer <access_token>
Content-Type: multipart/form-data
```

## Rate Limiting

API использует rate limiting для предотвращения злоупотреблений:
- **Лимит**: 2 запроса в секунду на IP адрес
- **Burst**: до 4 запросов в короткий период
- **Заголовки ответа**:
  - `X-RateLimit-Limit` - максимальное количество запросов
  - `X-RateLimit-Remaining` - оставшееся количество запросов
  - `X-RateLimit-Reset` - время сброса лимита

## Пагинация

В текущей версии API пагинация не реализована. Все эндпоинты возвращают полные списки данных. Рекомендуется добавить пагинацию для продакшн использования:

```
GET /v1/questions/all?page=1&limit=20
GET /v1/modules/all?page=2&limit=10
```

## Сортировка и фильтрация

В текущей версии сортировка и фильтрация ограничены. Рекомендуется добавить:

```
GET /v1/modules/all?level=1&sort=created_at&order=desc
GET /v1/questions/all?type=translation&difficulty=easy
```

## Версионирование API

API использует версионирование через URL path:
- Текущая версия: `v1`
- Базовый путь: `/v1/`

При внесении breaking changes будет создана новая версия API (v2, v3, и т.д.)

## CORS

API настроен для работы с CORS запросами. Поддерживаются следующие заголовки:
- `Access-Control-Allow-Origin`
- `Access-Control-Allow-Methods`
- `Access-Control-Allow-Headers`
- `Access-Control-Allow-Credentials`

## Безопасность

### JWT Токены
- **Access Token**: срок жизни 15 минут
- **Refresh Token**: срок жизни 30 дней
- Токены подписываются секретными ключами
- Refresh токены хранятся в HTTP-only cookies

### Валидация данных
- Все входные данные валидируются на сервере
- Используется whitelist подход для принимаемых полей
- SQL инъекции предотвращаются использованием prepared statements

### Хэширование паролей
- Пароли хэшируются с использованием bcrypt
- Cost factor: 12
- Пароли никогда не возвращаются в API ответах
```
